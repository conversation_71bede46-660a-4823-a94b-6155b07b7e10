<?php
require 'config.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set content type
header('Content-Type: application/json');

try {
    // Get and validate input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || empty($input['name'])) {
        echo json_encode(['success' => false, 'msg' => 'Missing name']);
        exit;
    }

    // Check if Razorpay credentials are configured
    if (RAZORPAY_KEY_ID === 'rzp_test_YOUR_KEY_ID' || RAZORPAY_KEY_SECRET === 'YOUR_KEY_SECRET') {
        echo json_encode([
            'success' => false,
            'msg' => 'Razorpay credentials not configured. Please update config.php with your actual Razorpay test credentials.'
        ]);
        exit;
    }

    $amount = CONTRIB_AMOUNT_PAISA;
    $orderData = [
        'amount' => $amount,
        'currency' => 'INR',
        'receipt' => 'rcpt_' . time(),
        'payment_capture' => 1
    ];

    // Initialize cURL
    $ch = curl_init('https://api.razorpay.com/v1/orders');
    curl_setopt($ch, CURLOPT_USERPWD, RAZORPAY_KEY_ID . ':' . RAZORPAY_KEY_SECRET);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($orderData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $result = curl_exec($ch);
    $http_status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);

    // Check for cURL errors
    if ($curl_error) {
        echo json_encode([
            'success' => false,
            'msg' => 'Network error: ' . $curl_error
        ]);
        exit;
    }

    // Check HTTP status
    if ($http_status !== 200 && $http_status !== 201) {
        $error_response = json_decode($result, true);
        $error_msg = 'HTTP ' . $http_status;

        if ($error_response && isset($error_response['error'])) {
            $error_msg .= ': ' . $error_response['error']['description'];
        }

        echo json_encode([
            'success' => false,
            'msg' => $error_msg,
            'debug' => $result
        ]);
        exit;
    }

    $order = json_decode($result, true);

    if (!$order || !isset($order['id'])) {
        echo json_encode([
            'success' => false,
            'msg' => 'Invalid response from Razorpay',
            'debug' => $result
        ]);
        exit;
    }

    echo json_encode([
        'success' => true,
        'order' => [
            'id' => $order['id'],
            'amount' => $order['amount']
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'msg' => 'Server error: ' . $e->getMessage()
    ]);
}
?>