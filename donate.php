<?php
require 'config.php';
?>
<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>Contribute ₹300 — 5000 Dost</title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
      line-height: 1.6;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 2rem;
    }

    .container {
      max-width: 500px;
      width: 100%;
    }

    .donation-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      padding: 3rem 2rem;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      text-align: center;
    }

    .back-link {
      position: absolute;
      top: 2rem;
      left: 2rem;
      color: white;
      text-decoration: none;
      font-size: 1.1rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.3s ease;
    }

    .back-link:hover {
      transform: translateX(-5px);
      color: rgba(255, 255, 255, 0.8);
    }

    .donation-header {
      margin-bottom: 2rem;
    }

    .donation-header h2 {
      font-size: 2rem;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 0.5rem;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .donation-header .subtitle {
      color: #718096;
      font-size: 1.1rem;
    }

    .amount-display {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      padding: 1.5rem;
      border-radius: 15px;
      margin-bottom: 2rem;
      font-size: 2rem;
      font-weight: 700;
      box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }

    .form-group {
      margin-bottom: 1.5rem;
      text-align: left;
    }

    .form-group label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #2d3748;
    }

    .form-group input {
      width: 100%;
      padding: 1rem 1.5rem;
      border: 2px solid #e2e8f0;
      border-radius: 12px;
      font-size: 1rem;
      transition: all 0.3s ease;
      background: white;
    }

    .form-group input:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      transform: translateY(-2px);
    }

    .form-group input::placeholder {
      color: #a0aec0;
    }

    .required {
      color: #e53e3e;
    }

    .submit-btn {
      width: 100%;
      padding: 1.2rem 2rem;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border: none;
      border-radius: 50px;
      font-size: 1.2rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
    }

    .submit-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
    }

    .submit-btn:active {
      transform: translateY(0);
    }

    .submit-btn:disabled {
      opacity: 0.7;
      cursor: not-allowed;
      transform: none;
    }

    .security-note {
      margin-top: 1.5rem;
      padding: 1rem;
      background: rgba(102, 126, 234, 0.1);
      border-radius: 10px;
      font-size: 0.9rem;
      color: #4a5568;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .loading {
      display: none;
      align-items: center;
      gap: 0.5rem;
    }

    .spinner {
      width: 20px;
      height: 20px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    @media (max-width: 768px) {
      body {
        padding: 1rem;
      }

      .donation-card {
        padding: 2rem 1.5rem;
      }

      .back-link {
        position: relative;
        top: auto;
        left: auto;
        color: #667eea;
        margin-bottom: 1rem;
        justify-content: center;
      }

      .donation-header h2 {
        font-size: 1.8rem;
      }

      .amount-display {
        font-size: 1.8rem;
      }
    }
  </style>
</head>

<body>
  <a href="index.php" class="back-link">
    <i class="fas fa-arrow-left"></i>
    Back to Home
  </a>

  <div class="container">
    <div class="donation-card">
      <div class="donation-header">
        <h2><i class="fas fa-heart"></i> Make a Contribution</h2>
        <p class="subtitle">Join our community of helpers</p>
      </div>

      <div class="amount-display">
        <i class="fas fa-rupee-sign"></i> 300
      </div>

      <form id="donorForm">
        <div class="form-group">
          <label for="name">Full Name <span class="required">*</span></label>
          <input type="text" id="name" placeholder="Enter your full name" required>
        </div>

        <div class="form-group">
          <label for="email">Email Address</label>
          <input type="email" id="email" placeholder="<EMAIL>">
        </div>

        <div class="form-group">
          <label for="city">City</label>
          <input type="text" id="city" placeholder="Your city">
        </div>

        <button type="submit" class="submit-btn" id="submitBtn">
          <span class="btn-text">
            <i class="fas fa-credit-card"></i>
            Proceed to Payment
          </span>
          <span class="loading">
            <div class="spinner"></div>
            Processing...
          </span>
        </button>

        <div class="security-note">
          <i class="fas fa-shield-alt"></i>
          Your payment is secured by Razorpay with 256-bit SSL encryption
        </div>
      </form>
    </div>
  </div>
  <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
  <script>
    document.getElementById('donorForm').addEventListener('submit', async function (e) {
      e.preventDefault();

      const submitBtn = document.getElementById('submitBtn');
      const btnText = submitBtn.querySelector('.btn-text');
      const loading = submitBtn.querySelector('.loading');

      // Show loading state
      submitBtn.disabled = true;
      btnText.style.display = 'none';
      loading.style.display = 'flex';

      try {
        const name = document.getElementById('name').value;
        const email = document.getElementById('email').value;
        const city = document.getElementById('city').value;

        const resp = await fetch('create_order.php', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ name, email, city })
        });

        const data = await resp.json();

        if (!data.success) {
          throw new Error(data.msg || 'Error creating order');
        }

        const order = data.order;
        const options = {
          "key": "<?= RAZORPAY_KEY_ID ?>",
          "amount": order.amount,
          "currency": "INR",
          "name": "5000 Dost — Wall of Kindness",
          "description": "Contribution ₹300",
          "order_id": order.id,
          "prefill": {
            "name": name,
            "email": email
          },
          "theme": {
            "color": "#667eea"
          },
          "handler": function (response) {
            // Show success loading
            btnText.innerHTML = '<i class="fas fa-check"></i> Payment Successful';
            btnText.style.display = 'flex';
            loading.style.display = 'none';

            fetch('payment_success.php', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                name, email, city,
                razorpay_order_id: response.razorpay_order_id,
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_signature: response.razorpay_signature
              })
            }).then(r => r.json()).then(res => {
              if (res.success) {
                // Show success message with animation
                const successMsg = document.createElement('div');
                successMsg.innerHTML = `
                  <div style="text-align: center; padding: 2rem; background: #f0fff4; border-radius: 15px; margin-top: 1rem; border: 2px solid #68d391;">
                    <i class="fas fa-check-circle" style="font-size: 3rem; color: #38a169; margin-bottom: 1rem;"></i>
                    <h3 style="color: #2d3748; margin-bottom: 0.5rem;">Thank You!</h3>
                    <p style="color: #4a5568;">Your contribution has been received successfully.</p>
                  </div>
                `;
                document.querySelector('.donation-card').appendChild(successMsg);

                setTimeout(() => {
                  window.location.href = 'hall.php';
                }, 2000);
              } else {
                throw new Error('Verification failed');
              }
            }).catch(err => {
              alert('Payment verification failed. Please contact support.');
              resetButton();
            });
          },
          "modal": {
            "ondismiss": function () {
              resetButton();
            }
          }
        };

        const rzp = new Razorpay(options);
        rzp.open();

      } catch (error) {
        alert(error.message || 'An error occurred. Please try again.');
        resetButton();
      }

      function resetButton() {
        submitBtn.disabled = false;
        btnText.style.display = 'flex';
        btnText.innerHTML = '<i class="fas fa-credit-card"></i> Proceed to Payment';
        loading.style.display = 'none';
      }
    });

    // Add form validation feedback
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
      input.addEventListener('blur', function () {
        if (this.hasAttribute('required') && !this.value.trim()) {
          this.style.borderColor = '#e53e3e';
        } else {
          this.style.borderColor = '#e2e8f0';
        }
      });

      input.addEventListener('input', function () {
        if (this.style.borderColor === 'rgb(229, 62, 62)') {
          this.style.borderColor = '#e2e8f0';
        }
      });
    });
  </script>
</body>

</html>