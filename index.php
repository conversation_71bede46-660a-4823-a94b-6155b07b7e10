<?php
require 'config.php';
$pdo = getPDO();
$stmt = $pdo->query("SELECT COUNT(*) as cnt, COALESCE(SUM(amount),0) as total FROM donors");
$row = $stmt->fetch();
$total_contributors = (int) $row['cnt'];
$total_amount = (int) $row['total'];
$goal_contributors = GOAL_CONTRIBUTORS;
$goal_amount = GOAL_AMOUNT_INR;
$percent = $goal_contributors ? min(100, round($total_contributors / $goal_contributors * 100, 1)) : 0;
?>
<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>5000 Dost — Wall of Kindness</title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
      line-height: 1.6;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
    }

    .hero {
      text-align: center;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      padding: 3rem 2rem;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
    }

    .hero h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 1rem;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .hero .subtitle {
      font-size: 1.2rem;
      color: #718096;
      margin-bottom: 2rem;
      font-weight: 400;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;
      margin: 2rem 0;
    }

    .stat-card {
      background: white;
      padding: 1.5rem;
      border-radius: 15px;
      text-align: center;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .stat-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }

    .stat-card .icon {
      font-size: 2rem;
      margin-bottom: 0.5rem;
      color: #667eea;
    }

    .stat-card .number {
      font-size: 2rem;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 0.5rem;
    }

    .stat-card .label {
      color: #718096;
      font-size: 0.9rem;
      font-weight: 500;
    }

    .progress-section {
      background: white;
      padding: 2rem;
      border-radius: 15px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
      margin: 2rem 0;
    }

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .progress-title {
      font-size: 1.2rem;
      font-weight: 600;
      color: #2d3748;
    }

    .progress-percentage {
      font-size: 1.5rem;
      font-weight: 700;
      color: #667eea;
    }

    .progress-bar {
      width: 100%;
      height: 12px;
      background: #e2e8f0;
      border-radius: 6px;
      overflow: hidden;
      margin-bottom: 1rem;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #667eea, #764ba2);
      border-radius: 6px;
      transition: width 0.8s ease;
      width:
        <?= $percent ?>
        %;
    }

    .progress-details {
      display: flex;
      justify-content: space-between;
      color: #718096;
      font-size: 0.9rem;
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
      margin-top: 2rem;
    }

    .btn {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 1rem 2rem;
      border: none;
      border-radius: 50px;
      font-size: 1.1rem;
      font-weight: 600;
      text-decoration: none;
      transition: all 0.3s ease;
      cursor: pointer;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .btn-primary {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    .btn-secondary {
      background: white;
      color: #667eea;
      border: 2px solid #667eea;
    }

    .btn-secondary:hover {
      background: #667eea;
      color: white;
      transform: translateY(-2px);
    }

    .floating-hearts {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
    }

    .heart {
      position: absolute;
      color: rgba(255, 255, 255, 0.1);
      font-size: 2rem;
      animation: float 6s ease-in-out infinite;
    }

    @keyframes float {

      0%,
      100% {
        transform: translateY(0px) rotate(0deg);
      }

      50% {
        transform: translateY(-20px) rotate(180deg);
      }
    }

    @media (max-width: 768px) {
      .container {
        padding: 1rem;
      }

      .hero {
        padding: 2rem 1.5rem;
      }

      .hero h1 {
        font-size: 2rem;
      }

      .stats-grid {
        grid-template-columns: 1fr;
      }

      .action-buttons {
        flex-direction: column;
        align-items: center;
      }

      .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
      }
    }
  </style>
</head>

<body>
  <div class="floating-hearts">
    <i class="fas fa-heart heart" style="top: 10%; left: 10%; animation-delay: 0s;"></i>
    <i class="fas fa-heart heart" style="top: 20%; left: 80%; animation-delay: 1s;"></i>
    <i class="fas fa-heart heart" style="top: 60%; left: 20%; animation-delay: 2s;"></i>
    <i class="fas fa-heart heart" style="top: 80%; left: 70%; animation-delay: 3s;"></i>
    <i class="fas fa-heart heart" style="top: 40%; left: 90%; animation-delay: 4s;"></i>
  </div>

  <div class="container">
    <div class="hero">
      <h1><i class="fas fa-hands-helping"></i> 5000 Dost — Wall of Kindness</h1>
      <p class="subtitle">Together we can make a difference, one contribution at a time</p>

      <div class="stats-grid">
        <div class="stat-card">
          <div class="icon"><i class="fas fa-users"></i></div>
          <div class="number"><?= number_format($total_contributors) ?></div>
          <div class="label">Contributors</div>
        </div>
        <div class="stat-card">
          <div class="icon"><i class="fas fa-rupee-sign"></i></div>
          <div class="number"><?= number_format($total_amount) ?></div>
          <div class="label">Raised (INR)</div>
        </div>
        <div class="stat-card">
          <div class="icon"><i class="fas fa-target"></i></div>
          <div class="number"><?= number_format(GOAL_AMOUNT_INR) ?></div>
          <div class="label">Goal (INR)</div>
        </div>
      </div>
    </div>

    <div class="progress-section">
      <div class="progress-header">
        <div class="progress-title">Campaign Progress</div>
        <div class="progress-percentage"><?= $percent ?>%</div>
      </div>
      <div class="progress-bar">
        <div class="progress-fill"></div>
      </div>
      <div class="progress-details">
        <span><?= $total_contributors ?> of <?= GOAL_CONTRIBUTORS ?> contributors</span>
        <span>₹<?= number_format($total_amount) ?> of ₹<?= number_format(GOAL_AMOUNT_INR) ?></span>
      </div>
    </div>

    <div class="action-buttons">
      <a href="donate.php" class="btn btn-primary">
        <i class="fas fa-heart"></i>
        Contribute ₹300
      </a>
      <a href="hall.php" class="btn btn-secondary">
        <i class="fas fa-trophy"></i>
        Hall of Helpers
      </a>
    </div>
  </div>

  <script>
    // Add some interactive animations
    document.addEventListener('DOMContentLoaded', function () {
      // Animate progress bar on load
      setTimeout(() => {
        const progressFill = document.querySelector('.progress-fill');
        progressFill.style.width = '<?= $percent ?>%';
      }, 500);

      // Add hover effects to stat cards
      const statCards = document.querySelectorAll('.stat-card');
      statCards.forEach(card => {
        card.addEventListener('mouseenter', function () {
          this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        card.addEventListener('mouseleave', function () {
          this.style.transform = 'translateY(0) scale(1)';
        });
      });
    });
  </script>
</body>

</html>