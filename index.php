<?php
require 'config.php';
$pdo = getPDO();
$stmt = $pdo->query("SELECT COUNT(*) as cnt, COALESCE(SUM(amount),0) as total FROM donors");
$row = $stmt->fetch();
$total_contributors = (int)$row['cnt'];
$total_amount = (int)$row['total'];
$goal_contributors = GOAL_CONTRIBUTORS;
$goal_amount = GOAL_AMOUNT_INR;
$percent = $goal_contributors ? min(100, round($total_contributors / $goal_contributors * 100, 1)) : 0;
?>
<!doctype html>
<html>
<head>
  <meta charset="utf-8">
  <title>5000 Dost — Wall of Kindness</title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
</head>
<body>
<h1>5000 Dost — Wall of Kindness</h1>
<p>Goal: <?= number_format(GOAL_AMOUNT_INR) ?> INR (<?= GOAL_CONTRIBUTORS ?> people × ₹300)</p>
<p>So far <?= $total_contributors ?> contributors, ₹<?= number_format($total_amount) ?> collected (<?= $percent ?>%)</p>
<a href="donate.php">Contribute ₹300</a> | <a href="hall.php">Hall of Helpers</a>
</body>
</html>